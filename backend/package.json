{"name": "xionpos-backend", "version": "1.0.0", "description": "backend for XionPOS web3 payment solution powered by Xion Protocol", "keywords": ["xionpos", "xion", "web3", "backend"], "homepage": "https://github.com/devprinceng/xionxepay#readme", "bugs": {"url": "https://github.com/devprinceng/xionxepay/issues"}, "repository": {"type": "git", "url": "git+https://github.com/devprinceng/xionxepay.git"}, "license": "ISC", "author": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "commonjs", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "rm -rf dist && tsc", "start": "node dist/index.js", "prod": "npm run build && npm start"}, "dependencies": {"@abstract-money/actions-xion": "^0.1.3", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}