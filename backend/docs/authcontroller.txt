Controller Checklist

post('api/auth/register)
1. register
- [ ] Validate name,email, password,phone,address,city,state,country,zip,businessName,category,metaAccountEmail from req.body
- [ ] Check if vendor exists
- [ ] Hash password (bcrypt)
- [ ] Save new vendor
- [ ] Generate JWT token
- [ ] Set token cookie
- [ ] Send welcome email (`nodemailer`)
- [ ] Respond with 201, token, message

sample request

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "Benjamin007$",
    "businessName": "John's Electronics",
    "businessDescription": "We sell top-quality electronic gadgets and accessories.",
    "category": "Electronics",
    "metaAccountEmail": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Tech Street",
    "city": "Lagos",
    "state": "Lagos",
    "country": "Nigeria",
    "zip": "100001"
}

post('api/auth/login)

2. login
- [ ] Validate email, password from req.body
- [ ] Check if vendor exists
- [ ] Compare password (bcrypt)
- [ ] Generate JWT token
- [ ] Set token cookie
- [ ] Respond with 200, token, message

sample request
{
    "email": "<EMAIL>",
    "password":"Benjamin007$"
}


post('api/auth/logout)
3. logout
- [ ] Clear token cookie
- [ ] Respond with 200, message

post('api/auth/sendVerifyOtp)
4. sendVerifyOtp
- [ ] Get vendorID from req.body
- [ ] Check if vendor exists and not verified
- [ ] Generate OTP (6 digits, 10 min expiry)
- [ ] Save OTP to vendor
- [ ] Send OTP email (`nodemailer`)
- [ ] Respond with 200, message

post('api/auth/verifyEmail)
5. verifyEmail
- [ ] Get vendorID, otp from req.body
- [ ] Check if vendor exists and not verified
- [ ] Validate OTP and expiry
- [ ] Mark account as verified
- [ ] Respond with 200, message

post('api/auth/sendResetOTP)
6. sendResetOTP
- [ ] Get email from req.body
- [ ] Check if vendor exists
- [ ] Generate OTP (6 digits, 10 min expiry)
- [ ] Save OTP to vendor
- [ ] Send OTP email (nodemailer)
- [ ] Respond with 200, message

post('api/auth/resetPassword)
7. resetPassword
- [ ] Get email, otp, newPassword from req.body
- [ ] Check if vendor exists
- [ ] Validate OTP and expiry
- [ ] Hash new password (bcrypt)
- [ ] Update vendor password
- [ ] Respond with 200, message

General
- [ ] All responses: JSON with success boolean and message
- [ ] Use JWT for authentication, cookies for session
- [ ] Use `nodemailer` for emails
- [ ] Hash passwords with bcrypt
- [ ] OTPs: 6 digits, 10 min validity
