{"name": "xionxepay-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.2.12", "lucide-react": "^0.400.0", "next": "14.2.5", "postcss": "^8.4.39", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.5", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "eslint": "^8.57.0", "eslint-config-next": "14.2.5"}}